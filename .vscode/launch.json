{"version": "0.2.0", "configurations": [{"name": "Flutter Debug (VM Service Fix)", "request": "launch", "type": "dart", "program": "lib/main.dart", "args": ["--vm-service-port=8888", "--enable-vm-service", "--disable-service-auth-codes"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 8888, "enableAsserts": true, "vmAdditionalArgs": ["--disable-service-auth-codes", "--enable-vm-service"]}, {"name": "Flutter Debug (Chrome)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterPlatform": "web", "webRenderer": "html", "args": ["--web-port=3000", "--web-hostname=localhost", "--vm-service-port=8889", "--enable-vm-service"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "vmServicePort": 8889}, {"name": "Flutter Debug (iOS)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterPlatform": "ios", "args": ["--vm-service-port=8890", "--enable-vm-service"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 8890, "enableAsserts": true}, {"name": "Flutter Debug (macOS)", "request": "launch", "type": "dart", "program": "lib/main.dart", "flutterPlatform": "macos", "args": ["--vm-service-port=8891", "--enable-vm-service"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 8891, "enableAsserts": true}, {"name": "Flutter Debug Console Test", "request": "launch", "type": "dart", "program": "lib/debug_test.dart", "args": ["--vm-service-port=8892", "--enable-vm-service"], "console": "debugConsole", "debugExternalPackageLibraries": true, "debugSdkLibraries": true, "showMemoryUsage": true, "vmServicePort": 8892, "enableAsserts": true}]}